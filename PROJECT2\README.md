# PROJECT2

## 项目简介
这是一个使用 Vue.js 构建的项目，旨在展示如何组织和管理 Vue 组件。

## 安装
1. 克隆项目：
   ```bash
   git clone <repository-url>
   ```
2. 进入项目目录：
   ```bash
   cd PROJECT2
   ```
3. 安装依赖：
   ```bash
   npm install
   ```

## 运行项目
使用以下命令启动开发服务器：
```bash
npm run dev
```

## 目录结构
- **src/**: 源代码目录
  - **assets/**: 存放静态资源
  - **components/**: Vue 组件
    - **One.vue**: 第一个组件
    - **Two.vue**: 第二个组件
    - **App.vue**: 根组件
  - **main.js**: 应用入口文件
- **public/**: 存放公共静态文件
- **index.html**: 应用的主 HTML 文件

## 贡献
欢迎任何形式的贡献！请提交问题或拉取请求。

## 许可证
本项目采用 MIT 许可证。